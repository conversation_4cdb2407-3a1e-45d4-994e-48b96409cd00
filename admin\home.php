<?php
// Start session
session_start();

// Check if the user is authenticated
if (!isset($_SESSION['auth_user']) || empty($_SESSION['auth_user']['id'])) {
    // User is not logged in, redirect to login page
    header('Location: login.php');
    exit; // Make sure to exit to prevent further script execution
}

// Include database connection
require_once 'database/conn.php';

?>
<?php include 'include/header.php'; ?>
    <!-- Preloader -->
    
    <!-- Main Wrapper -->
    <div id="main-wrapper" class="d-flex">
        <?php include 'include/sidebar.php'; ?>
       <!-- Content Wrapper -->
        <div class="content-wrapper">
            <?php include 'include/top-navbar.php'; ?>

            <!-- Main Content -->
          <div class="main-content">
            
          </div>
        </div>
    </div>
<?php include 'include/footer.php'; ?>
